"use client";

import { Button, Input } from "@heroui/react";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Spinner } from "@heroui/react";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "../../../../firebase";
import Cookies from "js-cookie";

export default function Login() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  // const handleLogin = async () => {
  //   try {
  //     const response = await axios.post("http://127.0.0.1:8000/api/login", {
  //       email,
  //       password,
  //     });

  //     sessionStorage.setItem("token", response.data.token);
  //     sessionStorage.setItem("email", email);

  //     if (response.status === 200) {
  //       setLoading(true);
  //       // localStorage.setItem("token", response.data.token);
  //       Cookies.set("token", response.data.token);

  //       router.push("/sections/dashboard");

  //       console.log("Login successful");
  //     }
  //   } catch (error) {
  //     // Handle error, e.g., show an error message
  //     setLoading(false);
  //     alert("Login failed. Please check your credentials.");
  //     console.error("Login failed", error);
  //   }
  // };

  //   const logoutToken = sessionStorage.getItem("token")
  //   console.log("Logout Token:", logoutToken);

  const handleLogin = async () => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      setLoading(true);
      const token = await userCredential.user.getIdToken();
      // Store token locally
      Cookies.set("token", token);

      console.log("User logged in:", userCredential.user);
      router.push("/sections/dashboard");
    } catch (error) {
      alert("Login failed");
      console.error(error);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced Professional Background - Gipahimo nang mas professional ang background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#DDEB9D] via-[#C8E6A0] to-[#A0C878]"></div>

      {/* Animated Background Shapes - Mga moving shapes para sa visual appeal */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large decorative circles - Dagko nga mga circles para sa depth */}
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/8 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-[#143D60]/3 rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 left-1/4 w-[500px] h-[500px] bg-gradient-to-r from-white/3 to-[#A0C878]/8 rounded-full blur-3xl"></div>

        {/* Professional grid pattern overlay - Subtle nga grid pattern */}
        <div className="absolute inset-0 opacity-3" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, #143D60 1px, transparent 0)`,
          backgroundSize: '60px 60px'
        }}></div>

        {/* Medical cross patterns - Mga medical cross sa background */}
        <div className="absolute top-20 left-20 w-6 h-6 opacity-10">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-6 bg-[#143D60] rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-1 bg-[#143D60] rounded-full"></div>
        </div>
        <div className="absolute bottom-32 right-32 w-4 h-4 opacity-10">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-[#143D60] rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-0.5 bg-[#143D60] rounded-full"></div>
        </div>
      </div>

      {/* Main Content Container - Split layout para sa professional look */}
      <div className="relative z-10 min-h-screen flex">

        {/* Left Side - Professional Introduction - Kaliwa nga side para sa introduction */}
        <div className="flex-1 flex items-center justify-center p-12 lg:p-16">
          <div className="max-w-xl">
            {/* Logo and Brand - Logo ug brand name */}
            <div className="mb-12">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-white to-gray-50 rounded-2xl mb-8 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
                {/* Medical Cross Icon - Medical cross icon gamit CSS */}
                <div className="relative">
                  <div className="w-8 h-8 bg-gradient-to-br from-[#143D60] to-[#1e4a6b] rounded-lg flex items-center justify-center shadow-lg">
                    <div className="w-6 h-6 relative">
                      {/* Cross symbol para sa health/medical theme */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-4 bg-white rounded-full"></div>
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-1 bg-white rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>

              <h1 className="text-6xl font-bold text-[#143D60] mb-4 tracking-tight leading-tight">
                Health<span className="text-[#A0C878]">Radar</span>
              </h1>
              <p className="text-2xl text-[#143D60] font-medium opacity-90 mb-8">
                Disease Management System
              </p>
            </div>

            {/* Professional Introduction - Professional nga introduction */}
            <div className="space-y-6 text-[#143D60]">
              <h2 className="text-3xl font-bold mb-6">
                Advanced Healthcare Analytics Platform
              </h2>

              <div className="space-y-4 text-lg leading-relaxed">
                <p className="font-medium">
                  Empowering healthcare professionals with comprehensive disease tracking,
                  real-time analytics, and predictive insights for better community health outcomes.
                </p>

                <div className="grid grid-cols-1 gap-4 mt-8">
                  {/* Feature highlights - Mga features nga highlight */}
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[#A0C878] rounded-full"></div>
                    <span className="font-medium">Real-time Disease Monitoring</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[#A0C878] rounded-full"></div>
                    <span className="font-medium">Predictive Analytics & Insights</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[#A0C878] rounded-full"></div>
                    <span className="font-medium">Municipality-based Data Management</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[#A0C878] rounded-full"></div>
                    <span className="font-medium">Interactive Geographic Visualization</span>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-6 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30">
                <p className="text-sm font-medium opacity-80">
                  &ldquo;Transforming healthcare data into actionable insights for healthier communities across the Philippines.&rdquo;
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form - Tuo nga side para sa login form */}
        <div className="w-full max-w-md lg:max-w-lg flex items-center justify-center p-8 lg:p-12">
          <div className="w-full">
            {/* Login Card - Enhanced with glassmorphism effect */}
            <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/30 relative overflow-hidden">
              {/* Subtle inner glow - Subtle nga inner glow para sa effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-[#A0C878]/5 rounded-3xl"></div>
              <div className="relative z-10">

                {/* Welcome Header - Welcome message para sa users */}
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-[#143D60] mb-3">Welcome Back</h2>
                  <p className="text-gray-600 text-lg">Please sign in to your account</p>
                </div>

                <form className="space-y-6" onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
                  {/* Email Input - Email input field */}
                  <div className="space-y-3">
                    <label htmlFor="email" className="text-sm font-semibold text-[#143D60] block uppercase tracking-wide">
                      Email Address
                    </label>
                    <Input
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      type="email"
                      placeholder="Enter your email address"
                      className="w-full"
                      classNames={{
                        input: "text-[#143D60] placeholder:text-gray-400 font-medium",
                        inputWrapper: "border-2 border-gray-200 hover:border-[#A0C878] focus-within:border-[#A0C878] bg-gray-50 hover:bg-white transition-all duration-300 h-14"
                      }}
                      size="lg"
                      radius="lg"
                      required
                    />
                  </div>

                  {/* Password Input - Password input field */}
                  <div className="space-y-3">
                    <label htmlFor="password" className="text-sm font-semibold text-[#143D60] block uppercase tracking-wide">
                      Password
                    </label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter your password"
                      className="w-full"
                      classNames={{
                        input: "text-[#143D60] placeholder:text-gray-400 font-medium",
                        inputWrapper: "border-2 border-gray-200 hover:border-[#A0C878] focus-within:border-[#A0C878] bg-gray-50 hover:bg-white transition-all duration-300 h-14"
                      }}
                      size="lg"
                      radius="lg"
                      required
                    />
                  </div>

                  {/* Forgot Password Link - Forgot password link */}
                  <div className="flex justify-end">
                    <Link href="#" className="text-sm text-[#EB5B00] hover:text-[#143D60] font-semibold transition-colors duration-200">
                      Forgot Password?
                    </Link>
                  </div>

                  {/* Login Button - Login button with loading state */}
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-[#143D60] to-[#1e4a6b] hover:from-[#1e4a6b] hover:to-[#143D60] text-white font-bold py-4 text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    onPress={handleLogin}
                    disabled={loading || !email || !password}
                    size="lg"
                    radius="lg"
                  >
                    {loading ? (
                      <div className="flex items-center gap-3">
                        <Spinner size="sm" color="white" />
                        <span>Signing In...</span>
                      </div>
                    ) : (
                      "Sign In"
                    )}
                  </Button>

                  {/* Divider - Divider para sa sign up section */}
                  <div className="relative my-8">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t-2 border-gray-200"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-6 bg-white text-gray-500 font-medium">New to HealthRadar?</span>
                    </div>
                  </div>

                  {/* Sign Up Button - Sign up button para sa new users */}
                  <Button
                    as={Link}
                    href="/components/signup"
                    className="w-full bg-gradient-to-r from-[#EB5B00] to-[#ff6b1a] hover:from-[#ff6b1a] hover:to-[#EB5B00] text-white font-bold py-4 text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    size="lg"
                    radius="lg"
                  >
                    Create New Account
                  </Button>
                </form>
              </div>
            </div>

            {/* Footer - Footer section */}
            <div className="text-center mt-8">
              <p className="text-[#143D60] font-medium opacity-80">© 2024 HealthRadar. All rights reserved.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
